<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.marketing.mapper.PaymentChangeRecordSummaryMapper">

    <resultMap type="PaymentChangeRecordSummary" id="PaymentChangeRecordSummaryResult">
        <result property="id"    column="id"    />
        <result property="paymentChangeRecordId"    column="payment_change_record_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="liveStatus"    column="live_status"    />
        <result property="careLevel"    column="care_level"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="bedNumber"    column="bed_number"    />
        <result property="totalPayment"    column="total_payment"    />
        <result property="discount"    column="discount"    />
        <result property="roomType"    column="room_type"    />
        <result property="contractNumber"    column="contract_number"    />
        <result property="contractDateFormatted"    column="contract_date_formatted"    />
        <result property="contractDate"    column="contract_date"    />
        <result property="feeCalculationDetails"    column="fee_calculation_details"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <!-- 费用变更汇总管理列表ResultMap -->
    <resultMap type="com.ruoyi.custom.admin.marketing.resp.PaymentChangeManagementResp" id="PaymentChangeManagementResult">
        <result property="paymentChangeId"    column="payment_change_id"    />
        <result property="contractNumber"    column="contract_number"    />
        <result property="customerName"    column="customer_name"    />
        <result property="liveStatus"    column="live_status"    />
        <result property="careLevel"    column="care_level"    />
        <result property="roomType"    column="room_type"    />
        <result property="bedNumber"    column="bed_number"    />
        <result property="paymentStatus"    column="payment_status"    />
    </resultMap>

    <sql id="selectPaymentChangeRecordSummaryVo">
        select id, payment_change_record_id, customer_name, live_status, care_level, payment_method, bed_number, total_payment, discount, room_type, contract_number, contract_date_formatted, contract_date, fee_calculation_details, del_flag, create_by, create_time, update_by, update_time, remark from t_payment_change_record_summary
    </sql>

    <select id="selectPaymentChangeRecordSummaryList" parameterType="PaymentChangeRecordSummary" resultMap="PaymentChangeRecordSummaryResult">
        <include refid="selectPaymentChangeRecordSummaryVo"/>
        <where>
            <if test="paymentChangeRecordId != null  and paymentChangeRecordId != ''"> and payment_change_record_id = #{paymentChangeRecordId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="liveStatus != null  and liveStatus != ''"> and live_status = #{liveStatus}</if>
            <if test="careLevel != null  and careLevel != ''"> and care_level like concat('%', #{careLevel}, '%')</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and payment_method = #{paymentMethod}</if>
            <if test="bedNumber != null  and bedNumber != ''"> and bed_number like concat('%', #{bedNumber}, '%')</if>
            <if test="totalPayment != null "> and total_payment = #{totalPayment}</if>
            <if test="discount != null  and discount != ''"> and discount = #{discount}</if>
            <if test="roomType != null  and roomType != ''"> and room_type like concat('%', #{roomType}, '%')</if>
            <if test="contractNumber != null  and contractNumber != ''"> and contract_number = #{contractNumber}</if>
            <if test="contractDateFormatted != null  and contractDateFormatted != ''"> and contract_date_formatted = #{contractDateFormatted}</if>
            <if test="contractDate != null "> and contract_date = #{contractDate}</if>
            <if test="delFlag != null  and delFlag != ''"> and del_flag = #{delFlag}</if>
        </where>
        order by create_time desc
    </select>

    <!-- 查询费用变更汇总管理列表 -->
    <select id="selectPaymentChangeManagementList" parameterType="PaymentChangeRecordSummary" resultMap="PaymentChangeManagementResult">
        SELECT
            s.contract_number,
            s.customer_name,
            s.live_status,
            s.care_level,
            s.room_type,
            s.bed_number,

            pcr.id as payment_change_id,
            pcr.payment_status
        FROM t_payment_change_record_summary s
        LEFT JOIN t_payment_change_record pcr ON s.payment_change_record_id = pcr.id
        WHERE s.del_flag = '0'
            <if test="contractNumber != null and contractNumber != ''">
                AND s.contract_number like concat('%', #{contractNumber}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND s.customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="liveStatus != null and liveStatus != ''">
                AND s.live_status = #{liveStatus}
            </if>
            <if test="careLevel != null and careLevel != ''">
                AND s.care_level like concat('%', #{careLevel}, '%')
            </if>
            <if test="roomType != null and roomType != ''">
                AND s.room_type like concat('%', #{roomType}, '%')
            </if>
            <if test="bedNumber != null and bedNumber != ''">
                AND s.bed_number like concat('%', #{bedNumber}, '%')
            </if>
            <if test="params != null and params.paymentStatus != null and params.paymentStatus != ''">
                AND pcr.payment_status = #{params.paymentStatus}
            </if>
            <if test="params != null and params.beginCreateTime != null and params.beginCreateTime != ''">
                AND date_format(s.create_time,'%Y-%m-%d') &gt;= date_format(#{params.beginCreateTime},'%Y-%m-%d')
            </if>
            <if test="params != null and params.endCreateTime != null and params.endCreateTime != ''">
                AND date_format(s.create_time,'%Y-%m-%d') &lt;= date_format(#{params.endCreateTime},'%Y-%m-%d')
            </if>
        ORDER BY s.create_time DESC
    </select>

    <select id="selectPaymentChangeRecordSummaryById" parameterType="String" resultMap="PaymentChangeRecordSummaryResult">
        <include refid="selectPaymentChangeRecordSummaryVo"/>
        where id = #{id}
    </select>

    <select id="selectPaymentChangeRecordSummaryByPaymentChangeRecordId" parameterType="String" resultMap="PaymentChangeRecordSummaryResult">
        <include refid="selectPaymentChangeRecordSummaryVo"/>
        where payment_change_record_id = #{paymentChangeRecordId} and del_flag = '0'
        limit 1
    </select>

    <insert id="insertPaymentChangeRecordSummary" parameterType="PaymentChangeRecordSummary">
        insert into t_payment_change_record_summary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="paymentChangeRecordId != null">payment_change_record_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="liveStatus != null">live_status,</if>
            <if test="careLevel != null">care_level,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="bedNumber != null">bed_number,</if>
            <if test="totalPayment != null">total_payment,</if>
            <if test="discount != null">discount,</if>
            <if test="roomType != null">room_type,</if>
            <if test="contractNumber != null">contract_number,</if>
            <if test="contractDateFormatted != null">contract_date_formatted,</if>
            <if test="contractDate != null">contract_date,</if>
            <if test="feeCalculationDetails != null">fee_calculation_details,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="paymentChangeRecordId != null">#{paymentChangeRecordId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="liveStatus != null">#{liveStatus},</if>
            <if test="careLevel != null">#{careLevel},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="bedNumber != null">#{bedNumber},</if>
            <if test="totalPayment != null">#{totalPayment},</if>
            <if test="discount != null">#{discount},</if>
            <if test="roomType != null">#{roomType},</if>
            <if test="contractNumber != null">#{contractNumber},</if>
            <if test="contractDateFormatted != null">#{contractDateFormatted},</if>
            <if test="contractDate != null">#{contractDate},</if>
            <if test="feeCalculationDetails != null">#{feeCalculationDetails},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePaymentChangeRecordSummary" parameterType="PaymentChangeRecordSummary">
        update t_payment_change_record_summary
        <trim prefix="SET" suffixOverrides=",">
            <if test="paymentChangeRecordId != null">payment_change_record_id = #{paymentChangeRecordId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="liveStatus != null">live_status = #{liveStatus},</if>
            <if test="careLevel != null">care_level = #{careLevel},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="bedNumber != null">bed_number = #{bedNumber},</if>
            <if test="totalPayment != null">total_payment = #{totalPayment},</if>
            <if test="discount != null">discount = #{discount},</if>
            <if test="roomType != null">room_type = #{roomType},</if>
            <if test="contractNumber != null">contract_number = #{contractNumber},</if>
            <if test="contractDateFormatted != null">contract_date_formatted = #{contractDateFormatted},</if>
            <if test="contractDate != null">contract_date = #{contractDate},</if>
            <if test="feeCalculationDetails != null">fee_calculation_details = #{feeCalculationDetails},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePaymentChangeRecordSummaryById" parameterType="String">
        delete from t_payment_change_record_summary where id = #{id}
    </delete>

    <delete id="deletePaymentChangeRecordSummaryByIds" parameterType="String">
        delete from t_payment_change_record_summary where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
