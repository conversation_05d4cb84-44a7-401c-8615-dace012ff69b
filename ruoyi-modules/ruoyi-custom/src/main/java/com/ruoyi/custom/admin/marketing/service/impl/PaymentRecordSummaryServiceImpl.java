package com.ruoyi.custom.admin.marketing.service.impl;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.StrUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.security.utils.DictUtils;
import lombok.SneakyThrows;

import com.ruoyi.custom.admin.marketing.domain.PaymentRecord;
import com.ruoyi.custom.admin.marketing.domain.PaymentRecordSummary;
import com.ruoyi.custom.admin.marketing.dto.PaymentRecordDTO;
import com.ruoyi.custom.admin.marketing.mapper.ContractInfoMapper;
import com.ruoyi.custom.admin.marketing.mapper.PaymentRecordMapper;
import com.ruoyi.custom.admin.marketing.mapper.PaymentRecordSummaryMapper;
import com.ruoyi.custom.admin.marketing.req.PaymentSummaryRequest;
import com.ruoyi.custom.admin.marketing.service.IPaymentRecordSummaryService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 缴费单汇总信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class PaymentRecordSummaryServiceImpl implements IPaymentRecordSummaryService {
    @Autowired
    private PaymentRecordSummaryMapper paymentRecordSummaryMapper;

    @Autowired
    private PaymentRecordMapper paymentRecordMapper;

    @Autowired
    private ContractInfoMapper contractInfoMapper;

    /**
     * 查询缴费单汇总信息
     *
     * @param id 缴费单汇总信息主键
     * @return 缴费单汇总信息
     */
    @Override
    public PaymentRecordSummary selectPaymentRecordSummaryById(String id) {
        return paymentRecordSummaryMapper.selectPaymentRecordSummaryById(id);
    }

    /**
     * 查询缴费单汇总信息列表
     *
     * @param paymentRecordSummary 缴费单汇总信息
     * @return 缴费单汇总信息
     */
    @Override
    public List<PaymentRecordSummary> selectPaymentRecordSummaryList(PaymentRecordSummary paymentRecordSummary) {
        return paymentRecordSummaryMapper.selectPaymentRecordSummaryList(paymentRecordSummary);
    }

    /**
     * 根据缴费记录ID查询汇总信息
     *
     * @param paymentRecordId 缴费记录ID
     * @return 缴费单汇总信息
     */
    @Override
    public PaymentRecordSummary selectPaymentRecordSummaryByPaymentRecordId(String paymentRecordId) {
        return paymentRecordSummaryMapper.selectPaymentRecordSummaryByPaymentRecordId(paymentRecordId);
    }

    /**
     * 新增缴费单汇总信息
     *
     * @param paymentRecordSummary 缴费单汇总信息
     * @return 结果
     */
    @Override
    public int insertPaymentRecordSummary(PaymentRecordSummary paymentRecordSummary) {
        paymentRecordSummary.setCreateTime(DateUtils.getNowDate());
        return paymentRecordSummaryMapper.insertPaymentRecordSummary(paymentRecordSummary);
    }

    /**
     * 修改缴费单汇总信息
     *
     * @param paymentRecordSummary 缴费单汇总信息
     * @return 结果
     */
    @Override
    public int updatePaymentRecordSummary(PaymentRecordSummary paymentRecordSummary) {
        paymentRecordSummary.setUpdateTime(DateUtils.getNowDate());
        return paymentRecordSummaryMapper.updatePaymentRecordSummary(paymentRecordSummary);
    }

    /**
     * 批量删除缴费单汇总信息
     *
     * @param ids 需要删除的缴费单汇总信息主键
     * @return 结果
     */
    @Override
    public int deletePaymentRecordSummaryByIds(String[] ids) {
        return paymentRecordSummaryMapper.deletePaymentRecordSummaryByIds(ids);
    }

    /**
     * 删除缴费单汇总信息信息
     *
     * @param id 缴费单汇总信息主键
     * @return 结果
     */
    @Override
    public int deletePaymentRecordSummaryById(String id) {
        return paymentRecordSummaryMapper.deletePaymentRecordSummaryById(id);
    }

    /**
     * 根据缴费记录ID删除汇总信息
     *
     * @param paymentRecordId 缴费记录ID
     * @return 结果
     */
    @Override
    public int deletePaymentRecordSummaryByPaymentRecordId(String paymentRecordId) {
        return paymentRecordSummaryMapper.deletePaymentRecordSummaryByPaymentRecordId(paymentRecordId);
    }



    /**
     * 根据缴费记录DTO和费用明细生成汇总信息
     *
     * @param paymentRecordDTO 缴费记录DTO
     * @param summaryRequest 费用明细请求
     * @return 缴费单汇总信息
     */
    @Override
    public PaymentRecordSummary generateSummaryFromDTO(PaymentRecordDTO paymentRecordDTO, PaymentSummaryRequest summaryRequest) {
        if (paymentRecordDTO == null) {
            throw new ServiceException("缴费记录DTO不能为空");
        }

        PaymentRecordSummary summary = new PaymentRecordSummary();
        summary.setId(IdUtils.fastUUID());
        summary.setPaymentRecordId(summaryRequest.getPaymentRecordId());
        summary.setDelFlag("0");

        // 从PaymentRecordDTO获取基本信息
        summary.setCustomerName(paymentRecordDTO.getElderlyName());
        summary.setCareLevel(DictUtils.selectDictLabel("care_level", paymentRecordDTO.getCareLevel()));
        summary.setBedNumber(paymentRecordDTO.getBedName());
        summary.setContractNumber(paymentRecordDTO.getContractNumber());
        summary.setContractDate(paymentRecordDTO.getContractStartDate());

        // 格式化签约日期
        if (paymentRecordDTO.getContractStartDate() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
            summary.setContractDateFormatted(sdf.format(paymentRecordDTO.getContractStartDate()));
        }

        // 缴费合计（从totalCost获取）
        summary.setTotalPayment(paymentRecordDTO.getTotalCost() != null ?
            paymentRecordDTO.getTotalCost() : BigDecimal.ZERO);

        // 从PaymentRecordDTO获取入住状态和房间类型
        summary.setLiveStatus(getLiveStatusName(paymentRecordDTO.getLiveState()));
        summary.setRoomType(paymentRecordDTO.getRoomTypeName());

        // 计算折扣（从床位折扣获取，转换为字符串格式）
        if (StrUtil.isNotBlank(paymentRecordDTO.getBedDiscount())) {
            try {
                BigDecimal discountValue = new BigDecimal(paymentRecordDTO.getBedDiscount());
                summary.setDiscount(convertDiscountToString(discountValue));
            } catch (NumberFormatException e) {
                summary.setDiscount("原价");
            }
        } else {
            summary.setDiscount("原价");
        }

        // 生成费用计算详情
        summary.setFeeCalculationDetails(generateFeeCalculationDetailsFromRequest(summaryRequest));

        return summary;
    }



    /**
     * 根据费用明细请求保存或更新汇总信息
     *
     * @param summaryRequest 费用明细请求
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveOrUpdateSummaryFromRequest(PaymentSummaryRequest summaryRequest) {
        if (summaryRequest == null || StrUtil.isBlank(summaryRequest.getPaymentRecordId())) {
            throw new ServiceException("缴费记录ID不能为空");
        }

        // 通过缴费记录ID获取PaymentRecord，然后获取合同号
        PaymentRecord paymentRecord = paymentRecordMapper.selectPaymentRecordById(summaryRequest.getPaymentRecordId());
        if (paymentRecord == null) {
            throw new ServiceException("缴费记录不存在");
        }

        // 通过合同号获取PaymentRecordDTO
        PaymentRecordDTO paymentRecordDTO = contractInfoMapper.selectLiveInfo(paymentRecord.getContractNumber());
        if (paymentRecordDTO == null) {
            throw new ServiceException("无法获取缴费记录详细信息");
        }

        // 查询是否已存在汇总信息
        PaymentRecordSummary existingSummary = selectPaymentRecordSummaryByPaymentRecordId(summaryRequest.getPaymentRecordId());

        if (existingSummary != null) {
            // 更新现有汇总信息
            PaymentRecordSummary updatedSummary = generateSummaryFromDTO(paymentRecordDTO, summaryRequest);
            updatedSummary.setId(existingSummary.getId());
            updatedSummary.setCreateBy(existingSummary.getCreateBy());
            updatedSummary.setCreateTime(existingSummary.getCreateTime());
            return updatePaymentRecordSummary(updatedSummary);
        } else {
            // 创建新的汇总信息
            PaymentRecordSummary newSummary = generateSummaryFromDTO(paymentRecordDTO, summaryRequest);
            return insertPaymentRecordSummary(newSummary);
        }
    }



    /**
     * 根据费用明细请求生成费用计算详情字符串
     */
    private String generateFeeCalculationDetailsFromRequest(PaymentSummaryRequest summaryRequest) {
        StringBuilder details = new StringBuilder();

        // 添加折扣说明
        if (StrUtil.isNotBlank(summaryRequest.getDiscountDescription())) {
            details.append("1. ").append(summaryRequest.getDiscountDescription()).append("。\n");
        }

        // 添加各项费用详情
        if (summaryRequest.getBedFee() != null && summaryRequest.getBedFee().compareTo(BigDecimal.ZERO) > 0) {
            if (StrUtil.isNotBlank(summaryRequest.getBedFeeDetails())) {
                details.append("床位费：").append(summaryRequest.getBedFeeDetails()).append("\n\n");
            } else {
                details.append("床位费：").append(summaryRequest.getBedFee()).append("元\n\n");
            }
        }

        if (summaryRequest.getCareFee() != null && summaryRequest.getCareFee().compareTo(BigDecimal.ZERO) > 0) {
            if (StrUtil.isNotBlank(summaryRequest.getCareFeeDetails())) {
                details.append("护理费：").append(summaryRequest.getCareFeeDetails()).append("\n\n");
            } else {
                details.append("护理费：").append(summaryRequest.getCareFee()).append("元\n\n");
            }
        }

        if (summaryRequest.getMealFee() != null && summaryRequest.getMealFee().compareTo(BigDecimal.ZERO) > 0) {
            if (StrUtil.isNotBlank(summaryRequest.getMealFeeDetails())) {
                details.append("餐费：").append(summaryRequest.getMealFeeDetails()).append("\n\n");
            } else {
                details.append("餐费：").append(summaryRequest.getMealFee()).append("元\n\n");
            }
        }

        if (summaryRequest.getAirConditioningFee() != null && summaryRequest.getAirConditioningFee().compareTo(BigDecimal.ZERO) > 0) {
            if (StrUtil.isNotBlank(summaryRequest.getAirConditioningFeeDetails())) {
                details.append("空调费：").append(summaryRequest.getAirConditioningFeeDetails()).append("\n\n");
            } else {
                details.append("空调费：").append(summaryRequest.getAirConditioningFee()).append("元\n\n");
            }
        }

        if (summaryRequest.getBeddingFee() != null && summaryRequest.getBeddingFee().compareTo(BigDecimal.ZERO) > 0) {
            if (StrUtil.isNotBlank(summaryRequest.getBeddingFeeDetails())) {
                details.append("床品费：").append(summaryRequest.getBeddingFeeDetails()).append("\n\n");
            } else {
                details.append("床品费：").append(summaryRequest.getBeddingFee()).append("元\n\n");
            }
        }

        if (summaryRequest.getMedicalDeposit() != null && summaryRequest.getMedicalDeposit().compareTo(BigDecimal.ZERO) > 0) {
            if (StrUtil.isNotBlank(summaryRequest.getMedicalDepositDetails())) {
                details.append("入住押金（医疗备用金）：").append(summaryRequest.getMedicalDepositDetails()).append("\n\n");
            } else {
                details.append("入住押金（医疗备用金）：").append(summaryRequest.getMedicalDeposit()).append("元\n\n");
            }
        }

        // 计算总计
        BigDecimal total = BigDecimal.ZERO;
        if (summaryRequest.getBedFee() != null) total = total.add(summaryRequest.getBedFee());
        if (summaryRequest.getCareFee() != null) total = total.add(summaryRequest.getCareFee());
        if (summaryRequest.getMealFee() != null) total = total.add(summaryRequest.getMealFee());
        if (summaryRequest.getAirConditioningFee() != null) total = total.add(summaryRequest.getAirConditioningFee());
        if (summaryRequest.getBeddingFee() != null) total = total.add(summaryRequest.getBeddingFee());
        if (summaryRequest.getMedicalDeposit() != null) total = total.add(summaryRequest.getMedicalDeposit());

        if (total.compareTo(BigDecimal.ZERO) > 0) {
            details.append("以上费用合计：");
            // 构建计算式
            StringBuilder calculation = new StringBuilder();
            if (summaryRequest.getBedFee() != null && summaryRequest.getBedFee().compareTo(BigDecimal.ZERO) > 0) {
                calculation.append(summaryRequest.getBedFee());
            }
            if (summaryRequest.getCareFee() != null && summaryRequest.getCareFee().compareTo(BigDecimal.ZERO) > 0) {
                if (calculation.length() > 0) calculation.append("+");
                calculation.append(summaryRequest.getCareFee());
            }
            if (summaryRequest.getMealFee() != null && summaryRequest.getMealFee().compareTo(BigDecimal.ZERO) > 0) {
                if (calculation.length() > 0) calculation.append("+");
                calculation.append(summaryRequest.getMealFee());
            }
            if (summaryRequest.getAirConditioningFee() != null && summaryRequest.getAirConditioningFee().compareTo(BigDecimal.ZERO) > 0) {
                if (calculation.length() > 0) calculation.append("+");
                calculation.append(summaryRequest.getAirConditioningFee());
            }
            if (summaryRequest.getBeddingFee() != null && summaryRequest.getBeddingFee().compareTo(BigDecimal.ZERO) > 0) {
                if (calculation.length() > 0) calculation.append("+");
                calculation.append(summaryRequest.getBeddingFee());
            }
            if (summaryRequest.getMedicalDeposit() != null && summaryRequest.getMedicalDeposit().compareTo(BigDecimal.ZERO) > 0) {
                if (calculation.length() > 0) calculation.append("+");
                calculation.append(summaryRequest.getMedicalDeposit());
            }
            details.append(calculation).append("=").append(total).append("元");
        }

        return details.toString();
    }



    /**
     * 获取入住状态名称
     */
    private String getLiveStatusName(String state) {
        if (StrUtil.isBlank(state)) {
            return "未知";
        }

        switch (state) {
            case "0":
                return "入住中";
            case "1":
                return "未入住";
            case "2":
                return "请假中";
            case "3":
                return "已退住";
            default:
                return "未知";
        }
    }

    /**
     * 将折扣数字转换为字符串格式
     */
    private String convertDiscountToString(BigDecimal discount) {
        if (discount == null) {
            return "原价";
        }

        if (discount.compareTo(BigDecimal.ONE) == 0) {
            return "原价";
        }

        // 将小数转换为折扣表示，如0.5 -> "五折"
        BigDecimal discountPercent = discount.multiply(new BigDecimal(10));
        int discountInt = discountPercent.intValue();

        String[] chineseNumbers = {"", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

        if (discountInt >= 1 && discountInt <= 9) {
            return chineseNumbers[discountInt] + "折";
        }

        return discount.toString() + "折";
    }

    /**
     * 导出客户价格确认单docx文档
     *
     * @param paymentRecordSummary 缴费单汇总信息
     * @param response HTTP响应
     */
    @SneakyThrows
    @Override
    public void exportPriceConfirmationReport(PaymentRecordSummary paymentRecordSummary, HttpServletResponse response) {
        /**
         * 准备模板数据
         */
        Map<String, Object> templateData = new HashMap<>();

        // 根据字段对应规则设置数据
        templateData.put("cdf", paymentRecordSummary.getContractDateFormatted() != null ?
            paymentRecordSummary.getContractDateFormatted() : "");
        templateData.put("cn", paymentRecordSummary.getCustomerName() != null ?
            paymentRecordSummary.getCustomerName() : "");
        templateData.put("ls", paymentRecordSummary.getLiveStatus() != null ?
            paymentRecordSummary.getLiveStatus() : "");
        templateData.put("bn", paymentRecordSummary.getBedNumber() != null ?
            paymentRecordSummary.getBedNumber() : "");
        templateData.put("cl", paymentRecordSummary.getCareLevel() != null ?
            paymentRecordSummary.getCareLevel() : "");
        templateData.put("tp", paymentRecordSummary.getTotalPayment() != null ?
            paymentRecordSummary.getTotalPayment().toString() : "0");
        templateData.put("d", paymentRecordSummary.getDiscount() != null ?
            paymentRecordSummary.getDiscount() : "原价");
        templateData.put("rt", paymentRecordSummary.getRoomType() != null ?
            paymentRecordSummary.getRoomType() : "");
        templateData.put("fcd", paymentRecordSummary.getFeeCalculationDetails() != null ?
            paymentRecordSummary.getFeeCalculationDetails() : "");
        templateData.put("ctn", paymentRecordSummary.getContractNumber() != null ?
            paymentRecordSummary.getContractNumber() : "");

        /**
         * 配置插件，渲染模板
         */
        Configure config = Configure.builder().build();

        XWPFTemplate template = XWPFTemplate
                .compile(ResourceUtil.getStream("templates/customerPriceConfirm.docx"), config)
                .render(templateData);

        /**
         * 设置响应头，触发浏览器下载
         */
        String fileName = "驻马店市为老服务中心客户价格确认单.docx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());

        response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        response.setContentType("application/octet-stream");
        // 使用 'filename*' 提供 utf-8 编码支持
        response.setHeader("Content-disposition", "attachment; filename=" + encodedFileName + ";filename*=utf-8''" + encodedFileName);

        /**
         * 输出到响应流
         */
        try (OutputStream out = response.getOutputStream();
             BufferedOutputStream bos = new BufferedOutputStream(out)) {
            template.write(bos);
            bos.flush();
        }

        // 关闭模板资源
        template.close();
    }
}
