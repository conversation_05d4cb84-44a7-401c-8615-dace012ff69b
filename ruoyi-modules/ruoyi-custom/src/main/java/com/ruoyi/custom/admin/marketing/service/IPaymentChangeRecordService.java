package com.ruoyi.custom.admin.marketing.service;

import com.ruoyi.custom.admin.marketing.domain.PaymentChangeRecord;

import java.util.List;


/**
 * 缴费确认单Service接口
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
public interface IPaymentChangeRecordService {
    /**
     * 查询缴费确认单
     *
     * @param id 缴费确认单主键
     * @return 缴费确认单
     */
    public PaymentChangeRecord selectPaymentChangeRecordById(String id);

    /**
     * 查询缴费确认单列表
     *
     * @param paymentChangeRecord 缴费确认单
     * @return 缴费确认单集合
     */
    public List<PaymentChangeRecord> selectPaymentChangeRecordList(PaymentChangeRecord paymentChangeRecord);

    /**
     * 新增缴费确认单
     *
     * @param paymentChangeRecord 缴费确认单
     * @return 结果
     */
    public int insertPaymentChangeRecord(PaymentChangeRecord paymentChangeRecord);

    /**
     * 修改缴费确认单
     *
     * @param paymentChangeRecord 缴费确认单
     * @return 结果
     */
    public int updatePaymentChangeRecord(PaymentChangeRecord paymentChangeRecord);

    /**
     * 批量删除缴费确认单
     *
     * @param ids 需要删除的缴费确认单主键集合
     * @return 结果
     */
    public int deletePaymentChangeRecordByIds(String[] ids);

    /**
     * 删除缴费确认单信息
     *
     * @param id 缴费确认单主键
     * @return 结果
     */
    public int deletePaymentChangeRecordById(String id);

    /**
     * 根据合同id，生成账单变更信息
     *
     * @param contractNumber
     * @param type
     * @return
     */
    PaymentChangeRecord generatePaymentChangeInfo(String contractNumber, String type);

    /**
     * 暂存费用变更单
     *
     * @param paymentChangeRecord 费用变更记录
     * @return 结果
     */
    String draftPaymentChangeRecord(PaymentChangeRecord paymentChangeRecord);

    /**
     * 确认费用变更单
     *
     * @param id 费用变更记录ID
     * @return 结果
     */
    int confirmPaymentChangeRecord(String id);

    /**
     * 修改暂存费用变更单
     *
     * @param paymentChangeRecord 费用变更记录
     * @return 结果
     */
    String updateDraftPaymentChangeRecord(PaymentChangeRecord paymentChangeRecord);

    /**
     * 删除暂存费用变更单
     *
     * @param id 费用变更记录ID
     * @return 结果
     */
    int deleteDraftPaymentChangeRecord(String id);
}

