package com.ruoyi.custom.admin.marketing.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.marketing.domain.PaymentRecordSummary;
import com.ruoyi.custom.admin.marketing.req.PaymentSummaryRequest;
import com.ruoyi.custom.admin.marketing.service.IPaymentRecordSummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 缴费单汇总信息Controller
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/paymentRecordSummary")
@Api(value = "缴费单汇总信息", tags = "缴费单汇总信息")
public class PaymentRecordSummaryController extends BaseController {
    @Autowired
    private IPaymentRecordSummaryService paymentRecordSummaryService;

    /**
     * 查询缴费单汇总信息列表
     */
    // @RequiresPermissions("custom:summary:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询缴费单汇总信息列表")
    public TableDataInfo list(PaymentRecordSummary paymentRecordSummary) {
        startPage();
        List<PaymentRecordSummary> list = paymentRecordSummaryService.selectPaymentRecordSummaryList(paymentRecordSummary);
        return getDataTable(list);
    }

    /**
     * 导出缴费单汇总信息列表
     */
    // @RequiresPermissions("custom:summary:export")
    @Log(title = "缴费单汇总信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出缴费单汇总信息列表")
    public void export(HttpServletResponse response, PaymentRecordSummary paymentRecordSummary) {
        List<PaymentRecordSummary> list = paymentRecordSummaryService.selectPaymentRecordSummaryList(paymentRecordSummary);
        ExcelUtil<PaymentRecordSummary> util = new ExcelUtil<PaymentRecordSummary>(PaymentRecordSummary.class);
        util.exportExcel(response, list, "缴费单汇总信息数据");
    }

    /**
     * 获取缴费单汇总信息详细信息
     */
    // @RequiresPermissions("custom:summary:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取缴费单汇总信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(paymentRecordSummaryService.selectPaymentRecordSummaryById(id));
    }

    /**
     * 根据缴费记录ID获取汇总信息
     */
    @GetMapping("/byPaymentRecordId/{paymentRecordId}")
    @ApiOperation(value = "根据缴费记录ID获取汇总信息")
    public AjaxResult getInfoByPaymentRecordId(@PathVariable("paymentRecordId") String paymentRecordId) {
        PaymentRecordSummary summary = paymentRecordSummaryService.selectPaymentRecordSummaryByPaymentRecordId(paymentRecordId);
        return AjaxResult.success(summary);
    }

    /**
     * 新增缴费单汇总信息
     */
    // @RequiresPermissions("custom:summary:add")
    @Log(title = "缴费单汇总信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增缴费单汇总信息")
    public AjaxResult add(@RequestBody PaymentRecordSummary paymentRecordSummary) {
        return toAjax(paymentRecordSummaryService.insertPaymentRecordSummary(paymentRecordSummary));
    }

    /**
     * 修改缴费单汇总信息
     */
    // @RequiresPermissions("custom:summary:edit")
    @Log(title = "缴费单汇总信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改缴费单汇总信息")
    public AjaxResult edit(@RequestBody PaymentRecordSummary paymentRecordSummary) {
        return toAjax(paymentRecordSummaryService.updatePaymentRecordSummary(paymentRecordSummary));
    }

    /**
     * 删除缴费单汇总信息
     */
    // @RequiresPermissions("custom:summary:remove")
    @Log(title = "缴费单汇总信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除缴费单汇总信息")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(paymentRecordSummaryService.deletePaymentRecordSummaryByIds(ids));
    }

    /**
     * 保存或更新缴费单汇总信息（根据费用明细）
     */
    @Log(title = "缴费单汇总信息", businessType = BusinessType.INSERT)
    @PostMapping("/saveFromRequest")
    @ApiOperation(value = "保存或更新缴费单汇总信息（根据费用明细）")
    public AjaxResult saveFromRequest(@RequestBody PaymentSummaryRequest summaryRequest) {
        return toAjax(paymentRecordSummaryService.saveOrUpdateSummaryFromRequest(summaryRequest));
    }

    /**
     * 导出客户价格确认单docx文档
     */
    // @RequiresPermissions("custom:summary:export")
    @Log(title = "缴费单汇总信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportPriceConfirmation")
    @ApiOperation(value = "导出客户价格确认单docx文档")
    public void exportPriceConfirmation(HttpServletResponse response, @RequestParam String paymentRecordId) {
        // 通过缴费记录ID获取汇总信息
        PaymentRecordSummary paymentRecordSummary = paymentRecordSummaryService.selectPaymentRecordSummaryByPaymentRecordId(paymentRecordId);
        if (paymentRecordSummary == null) {
            throw new ServiceException("缴费单汇总信息不存在");
        }
        paymentRecordSummaryService.exportPriceConfirmationReport(paymentRecordSummary, response);
    }
}
