package com.ruoyi.custom.admin.marketing.service;

import com.ruoyi.custom.admin.marketing.domain.PaymentRecord;
import com.ruoyi.custom.admin.marketing.domain.PaymentRecordSummary;
import com.ruoyi.custom.admin.marketing.dto.PaymentRecordDTO;
import com.ruoyi.custom.admin.marketing.req.PaymentSummaryRequest;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 缴费单汇总信息Service接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface IPaymentRecordSummaryService {
    /**
     * 查询缴费单汇总信息
     *
     * @param id 缴费单汇总信息主键
     * @return 缴费单汇总信息
     */
    public PaymentRecordSummary selectPaymentRecordSummaryById(String id);

    /**
     * 查询缴费单汇总信息列表
     *
     * @param paymentRecordSummary 缴费单汇总信息
     * @return 缴费单汇总信息集合
     */
    public List<PaymentRecordSummary> selectPaymentRecordSummaryList(PaymentRecordSummary paymentRecordSummary);

    /**
     * 根据缴费记录ID查询汇总信息
     *
     * @param paymentRecordId 缴费记录ID
     * @return 缴费单汇总信息
     */
    public PaymentRecordSummary selectPaymentRecordSummaryByPaymentRecordId(String paymentRecordId);

    /**
     * 新增缴费单汇总信息
     *
     * @param paymentRecordSummary 缴费单汇总信息
     * @return 结果
     */
    public int insertPaymentRecordSummary(PaymentRecordSummary paymentRecordSummary);

    /**
     * 修改缴费单汇总信息
     *
     * @param paymentRecordSummary 缴费单汇总信息
     * @return 结果
     */
    public int updatePaymentRecordSummary(PaymentRecordSummary paymentRecordSummary);

    /**
     * 批量删除缴费单汇总信息
     *
     * @param ids 需要删除的缴费单汇总信息主键集合
     * @return 结果
     */
    public int deletePaymentRecordSummaryByIds(String[] ids);

    /**
     * 删除缴费单汇总信息信息
     *
     * @param id 缴费单汇总信息主键
     * @return 结果
     */
    public int deletePaymentRecordSummaryById(String id);

    /**
     * 根据缴费记录ID删除汇总信息
     *
     * @param paymentRecordId 缴费记录ID
     * @return 结果
     */
    public int deletePaymentRecordSummaryByPaymentRecordId(String paymentRecordId);

    /**
     * 根据缴费记录DTO和费用明细生成汇总信息
     *
     * @param paymentRecordDTO 缴费记录DTO
     * @param summaryRequest 费用明细请求
     * @return 缴费单汇总信息
     */
    public PaymentRecordSummary generateSummaryFromDTO(PaymentRecordDTO paymentRecordDTO, PaymentSummaryRequest summaryRequest);

    /**
     * 根据费用明细请求保存或更新汇总信息
     *
     * @param summaryRequest 费用明细请求
     * @return 结果
     */
    public int saveOrUpdateSummaryFromRequest(PaymentSummaryRequest summaryRequest);

    /**
     * 导出客户价格确认单docx文档
     *
     * @param paymentRecordSummary 缴费单汇总信息
     * @param response HTTP响应
     */
    public void exportPriceConfirmationReport(PaymentRecordSummary paymentRecordSummary, HttpServletResponse response);
}
