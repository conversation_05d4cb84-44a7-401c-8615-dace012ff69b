package com.ruoyi.custom.admin.marketing.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.balance.service.IBalanceInfoService;
import com.ruoyi.custom.admin.fee.domain.req.FeePaySaveVo;
import com.ruoyi.custom.admin.fee.service.IFeePayInfoService;
import com.ruoyi.custom.admin.liveManage.domain.LiveComboRecords;
import com.ruoyi.custom.admin.liveManage.domain.LiveMealComboRecord;
import com.ruoyi.custom.admin.liveManage.service.ILiveBaseInfoService;
import com.ruoyi.custom.admin.marketing.constant.FeeType;
import com.ruoyi.custom.admin.marketing.domain.PaymentChangeRecord;
import com.ruoyi.custom.admin.marketing.domain.PaymentRecord;
import com.ruoyi.custom.admin.marketing.dto.PaymentRecordDTO;
import com.ruoyi.custom.admin.marketing.mapper.ContractInfoMapper;
import com.ruoyi.custom.admin.marketing.mapper.PaymentChangeRecordMapper;
import com.ruoyi.custom.admin.marketing.service.IPaymentChangeRecordService;
import com.ruoyi.custom.admin.marketing.service.IPaymentRecordService;
import com.ruoyi.custom.utils.OrderUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.custom.admin.marketing.constant.FeeType.*;

/**
 * 缴费确认单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@Service
public class PaymentChangeRecordServiceImpl implements IPaymentChangeRecordService {
    @Autowired
    private PaymentChangeRecordMapper paymentChangeRecordMapper;

    @Autowired
    private ContractInfoMapper contractInfoMapper;

    @Autowired
    private IPaymentRecordService paymentRecordService;

    @Autowired
    private IFeePayInfoService feePayInfoService;

    @Autowired
    private IBalanceInfoService balanceInfoService;

    @Autowired
    private ILiveBaseInfoService liveBaseInfoService;

    /**
     * 查询缴费确认单
     *
     * @param id 缴费确认单主键
     * @return 缴费确认单
     */
    @Override
    public PaymentChangeRecord selectPaymentChangeRecordById(String id) {
        return paymentChangeRecordMapper.selectPaymentChangeRecordById(id);
    }

    /**
     * 查询缴费确认单列表
     *
     * @param paymentChangeRecord 缴费确认单
     * @return 缴费确认单
     */
    @Override
    public List<PaymentChangeRecord> selectPaymentChangeRecordList(PaymentChangeRecord paymentChangeRecord) {
        return paymentChangeRecordMapper.selectPaymentChangeRecordList(paymentChangeRecord);
    }

    /**
     * 新增缴费确认单
     *
     * @param paymentChangeRecord 缴费确认单
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertPaymentChangeRecord(PaymentChangeRecord paymentChangeRecord) {
        // 账户变动
        balanceChange(paymentChangeRecord);

        // 套餐变动生效
        dealWithFeeType(paymentChangeRecord);

        // 保存变更单
        try {
            paymentChangeRecord.setCreateTime(DateUtils.getNowDate());
            paymentChangeRecord.setId(OrderUtils.getChangeBillCode());
            paymentChangeRecordMapper.insertPaymentChangeRecord(paymentChangeRecord);
        } catch (DuplicateKeyException e) {
            // 同步缓存中最新的单号
            String maxId = paymentChangeRecordMapper.selectMaxId();
            if (maxId != null) {
                OrderUtils.setNumber(OrderUtils.BILL_ORDER2, Integer.parseInt(maxId.split("-")[2]));
            }
            paymentChangeRecord.setId(OrderUtils.getBillCode());
            return paymentChangeRecordMapper.insertPaymentChangeRecord(paymentChangeRecord);
        }

        return 1;
    }

    private void dealWithFeeType(PaymentChangeRecord paymentChangeRecord) {
        for (PaymentChangeRecord.Detail detail : paymentChangeRecord.getDetails()) {
            FeeType feeType = FeeType.fromCode(detail.getType());
            switch (feeType) {
                case BED_FEE:
                    // 更换房间
                    if (StrUtil.isNotBlank(paymentChangeRecord.getLiveId())
                            && detail.getOldBedId() != null
                            && StrUtil.isNotBlank(detail.getChangedItem().getString("bedId"))
                            && StrUtil.isNotBlank(detail.getChangedItem().getString("roomId"))
                            && StrUtil.isNotBlank(detail.getChangedItem().getString("roomVersion"))
                    ) {
                        liveBaseInfoService.changeBed(paymentChangeRecord.getLiveId(), detail.getOldBedId(),
                                detail.getChangedItem().getLong("bedId"), detail.getChangedItem().getString("roomId"), detail.getChangedItem().getString("roomVersion"));
                    }
                    break;

                case CARE_FEE:
                    // 更换护理套餐
                    if (StrUtil.isNotBlank(paymentChangeRecord.getLiveId())
                            && StrUtil.isNotBlank(detail.getChangedItem().getString("careLevel"))
                            && StrUtil.isNotBlank(detail.getChangedItem().getString("careComboId"))
                            && StrUtil.isNotBlank(detail.getChangedItem().getString("careComboFeeVersion"))
                    ) {
                        LiveComboRecords liveComboRecords = new LiveComboRecords();
                        liveComboRecords.setLiveId(paymentChangeRecord.getLiveId());
                        liveComboRecords.setCareLevel(detail.getChangedItem().getString("careLevel"));
                        liveComboRecords.setComboId(detail.getChangedItem().getString("careComboId"));
                        liveComboRecords.setComboVersion(detail.getChangedItem().getString("careComboFeeVersion"));
                        liveComboRecords.setState("0"); // 在用
                        liveBaseInfoService.changeUserCombo(liveComboRecords);
                    }
                    break;

                case MEAL_FEE:
                    // 更换餐品套餐
                    if (StrUtil.isNotBlank(paymentChangeRecord.getLiveId())
                            && detail.getChangedItem().getLong("mealComboId") != null
                            && detail.getChangedItem().getLong("mealComboFeeId") != null
                    ) {
                    }
                    LiveMealComboRecord liveMealComboRecord = new LiveMealComboRecord();
                    liveMealComboRecord.setLiveId(paymentChangeRecord.getLiveId());
                    liveMealComboRecord.setMealId(detail.getChangedItem().getLong("mealComboId"));
                    liveMealComboRecord.setMealFeeId(detail.getChangedItem().getLong("mealComboFeeId"));
                    liveMealComboRecord.setStatus("0"); // 生效
                    liveBaseInfoService.changeMealCombo(liveMealComboRecord);
                    break;

                case ACCOUNT_DEDUCTION:
                    break;

                default:
                    break;
            }
        }
    }

    private void balanceChange(PaymentChangeRecord paymentChangeRecord) {
        // 本次账户变动金额
        BigDecimal accountChangeCost = paymentChangeRecord.getAccountAddCost();

        // 账户变动
        if (accountChangeCost.compareTo(BigDecimal.ZERO) > 0) { // 付款流程
            balanceInfoService.doDeduction(paymentChangeRecord.getElderlyId(), String.valueOf(SecurityUtils.getUserId()), accountChangeCost.abs(),  "3");
        } else if (accountChangeCost.compareTo(BigDecimal.ZERO) < 0) { // 退费流程
            FeePaySaveVo feePaySaveVo = new FeePaySaveVo();
            feePaySaveVo.setUserId(paymentChangeRecord.getElderlyId());
            feePaySaveVo.setPayAmount(accountChangeCost.abs());
            // feePaySaveVo.setPayType("1");
            feePaySaveVo.setConsumeAccounType("2"); // 收入
            feePaySaveVo.setOperatorId(String.valueOf(SecurityUtils.getUserId()));
            feePayInfoService.insertFeePaySaveVo(feePaySaveVo, "2");
        }

    }

    /**
     * 修改缴费确认单
     *
     * @param paymentChangeRecord 缴费确认单
     * @return 结果
     */
    @Override
    public int updatePaymentChangeRecord(PaymentChangeRecord paymentChangeRecord) {
        return paymentChangeRecordMapper.updatePaymentChangeRecord(paymentChangeRecord);
    }

    /**
     * 批量删除缴费确认单
     *
     * @param ids 需要删除的缴费确认单主键
     * @return 结果
     */
    @Override
    public int deletePaymentChangeRecordByIds(String[] ids) {
        return paymentChangeRecordMapper.deletePaymentChangeRecordByIds(ids);
    }

    /**
     * 删除缴费确认单信息
     *
     * @param id 缴费确认单主键
     * @return 结果
     */
    @Override
    public int deletePaymentChangeRecordById(String id) {
        return paymentChangeRecordMapper.deletePaymentChangeRecordById(id);
    }

    /**
     * 根据合同id，生成账单变更信息
     *
     * @param contractNumber
     * @param type
     * @return
     */
    @Override
    public PaymentChangeRecord generatePaymentChangeInfo(String contractNumber, String type) {
        // 获取基础信息
        PaymentRecordDTO paymentRecordDTO = contractInfoMapper.selectLiveInfo(contractNumber);
        if (paymentRecordDTO.getLiveState() == null) {
            throw new ServiceException("当前老人未入住");
        }

        //  查询该合同所有缴费记录
        List<PaymentRecord> historicalPaymentRecords = paymentRecordService.fetchPaymentRecords(contractNumber);
        if (CollUtil.isEmpty(historicalPaymentRecords)) {
            throw new ServiceException("此合同没有缴费记录");
        }

        // 构建变更表单基础信息
        PaymentChangeRecord paymentChangeRecordRes = BeanUtil.copyProperties(paymentRecordDTO, PaymentChangeRecord.class, "details");
        paymentChangeRecordRes.setDetails(new ArrayList<>());

        // 查询该合同的所有历史变更单记录 (PaymentChangeRecord)，按ID倒序排列 (最新的在前)
        List<PaymentChangeRecord> historicalChangeRecords = paymentChangeRecordMapper.selectByContractNumberOrderByIdDesc(contractNumber);

        // 确定需要处理的费用类型
        FeeType[] needFeeTypes;
        if ("1".equals(type)) {
            needFeeTypes = new FeeType[]{BED_FEE, CARE_FEE, ACCOUNT_DEDUCTION};
        } else {
            needFeeTypes = new FeeType[]{MEAL_FEE, ACCOUNT_DEDUCTION};
        }
        for (FeeType feeType : needFeeTypes) {
            PaymentChangeRecord.Detail detail = buildDetail(feeType, paymentRecordDTO, historicalPaymentRecords, historicalChangeRecords);
            paymentChangeRecordRes.getDetails().add(detail);
        }

        return paymentChangeRecordRes;
    }

    private PaymentChangeRecord.Detail buildDetail(FeeType feeType,
                                                   PaymentRecordDTO dto,
                                                   List<PaymentRecord> historicalPaymentRecords,
                                                   List<PaymentChangeRecord> historicalChangeRecords) {

        PaymentChangeRecord.Detail detail = new PaymentChangeRecord.Detail();
        detail.setType(feeType.getCode());
        detail.setChangedAmount(BigDecimal.ZERO); // 默认为0
        // 初始化，避免在 calculateAndSetFeeDetails 未覆盖时为 null
        detail.setPaidAmount(BigDecimal.ZERO);
        detail.setStartDate(null);

        switch (feeType) {
            case BED_FEE:
                detail.setTypeName(feeType.getDescription() + (dto.getRoomTypeName() == null ? "" : "（" + dto.getRoomTypeName() + "）"));
                detail.setOldBedId(dto.getBedId());
                detail.setOriginalFeeStandard(dto.getRoomCost() == null ? BigDecimal.ZERO : dto.getRoomCost());
                // 设置折扣
                JSONObject changedItem = new JSONObject();
                changedItem.put("discount", dto.getBedDiscount() == null ? new BigDecimal("1") : new BigDecimal(dto.getBedDiscount()));
                detail.setChangedItem(changedItem);

                calculateAndSetFeeDetails(detail, feeType, dto, historicalPaymentRecords, historicalChangeRecords);
                break;

            case CARE_FEE:
                detail.setTypeName(feeType.getDescription() + (dto.getComboName() == null ? "" : "（" + dto.getComboName() + "）"));
                detail.setOriginalFeeStandard(dto.getComboCost() == null ? BigDecimal.ZERO : dto.getComboCost());

                calculateAndSetFeeDetails(detail, feeType, dto, historicalPaymentRecords, historicalChangeRecords);
                break;

            case MEAL_FEE:
                detail.setTypeName(feeType.getDescription() + (dto.getMealName() == null ? "" : "（" + dto.getMealName() + "）"));
                detail.setOriginalFeeStandard(dto.getMealCost() == null ? BigDecimal.ZERO : dto.getMealCost());

                calculateAndSetFeeDetails(detail, feeType, dto, historicalPaymentRecords, historicalChangeRecords);
                break;

            case ACCOUNT_DEDUCTION:
                detail.setTypeName(feeType.getDescription());
                detail.setOriginalFeeStandard(dto.getAccountBalance() == null ? BigDecimal.ZERO : dto.getAccountBalance());
                // 账户抵扣通常paidAmount为0或余额本身，startDate不适用
                detail.setPaidAmount(BigDecimal.ZERO); // 或者根据业务设置为余额
                detail.setStartDate(null);
                break;

            default:
                // 对于未明确处理的费用类型，确保关键字段有默认值
                detail.setTypeName(feeType.getDescription());
                if (detail.getOriginalFeeStandard() == null) { // 确保不为null
                    detail.setOriginalFeeStandard(BigDecimal.ZERO);
                }
                // paidAmount 和 startDate 已在 switch 前初始化
                break;
        }
        return detail;
    }

    /**
     * 为特定费用类型计算并设置"最近变更开始日期"(startDate)和"（基于该变更的）已缴费金额"(paidAmount)。
     * "最近变更开始日期"实际上是与该费用类型相关的、最新的、且具有有效endDate的变更单中的endDate。
     * "已缴费金额"是根据上述最新相关变更单的ID，在历史缴费中累加的、与该变更单ID关联的缴费额。
     *
     * @param detailToUpdate           需要更新startDate和paidAmount的PaymentChangeRecord.Detail对象
     * @param feeType                  当前处理的费用类型
     * @param dto                      当前老人和合同的基础信息DTO (目前主要用于获取默认日期，如合同开始日期)
     * @param historicalPaymentRecords 该合同的所有历史缴费记录 (PaymentRecord列表)
     * @param historicalChangeRecords  该合同的所有历史变更单记录 (PaymentChangeRecord列表, 应已按ID倒序)
     */
    private void calculateAndSetFeeDetails(PaymentChangeRecord.Detail detailToUpdate,
                                           FeeType feeType,
                                           PaymentRecordDTO dto,
                                           List<PaymentRecord> historicalPaymentRecords,
                                           List<PaymentChangeRecord> historicalChangeRecords) {

        // 1. 从历史变更单中找到与当前feeType相关、且endDate有效的最新变更记录的Optional
        //    这将包含变更单本身，方便后续提取ID和endDate
        final Optional<PaymentChangeRecord> latestRelevantChangeOpt = CollUtil.isNotEmpty(historicalChangeRecords) ?
                historicalChangeRecords.stream()
                        .filter(Objects::nonNull) // 过滤掉null的变更单对象
                        .filter(change -> CollUtil.isNotEmpty(change.getDetails())) // 过滤出包含详情的变更单
                        .filter(change -> change.getDetails().stream() // 进一步筛选，确保详情中有目标费用类型且endDate有效
                                .anyMatch(d -> feeType.getCode().equals(d.getType()) && d.getEndDate() != null)
                        )
                        .findFirst() // 获取第一个符合条件的（即最新的）
                : Optional.empty(); // 如果历史变更单列表为空，直接返回空的Optional

        if (latestRelevantChangeOpt.isPresent()) {
            // 存在历史变更单的情况下的处理逻辑

            // 2. 从上一步骤的结果中，安全地获取"最近变更开始日期" (endDate)
            final Date latestValidChangeEndDate = latestRelevantChangeOpt
                    .flatMap(latestRelevantChange -> latestRelevantChange.getDetails().stream() // 扁平化详情列表
                            .filter(d -> feeType.getCode().equals(d.getType()) && d.getEndDate() != null) // 再次过滤，精确找到该费用类型的有效endDate
                            .map(PaymentChangeRecord.Detail::getEndDate) // 映射到endDate
                            .findFirst()) // 获取第一个匹配的endDate
                    .orElse(null); // 如果Optional为空或内部没有找到，则为null
            detailToUpdate.setStartDate(latestValidChangeEndDate); // 设置到变更详情对象

            // 3. 从上一步骤的结果中，安全地获取"关联的变更单ID"
            final String associatedChangeRecordId = latestRelevantChangeOpt
                    .map(PaymentChangeRecord::getId) // 如果Optional不为空，则映射到其ID
                    .orElse(null); // 否则为null


            // 4. 根据获取的 associatedChangeRecordId 计算"（基于该变更的）已缴费金额"
            BigDecimal paidAmount = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(historicalPaymentRecords)) {
                // 确保缴费记录按时间倒序处理，以便正确判断"最近一次缴费是否关联到最新变更"
                List<PaymentRecord> sortedPaymentRecords = historicalPaymentRecords.stream()
                        .filter(pr -> pr.getPaymentTime() != null) // 过滤掉缴费时间为空的记录
                        .sorted(Comparator.comparing(PaymentRecord::getPaymentTime).reversed()) // 按缴费时间降序排序
                        .collect(Collectors.toList());

                if (StrUtil.isNotBlank(associatedChangeRecordId)) {
                    // 检查最新的缴费记录中，是否有与当前feeType和associatedChangeRecordId匹配的缴费项
                    final Optional<PaymentRecord.Detail> firstRelevantPaymentDetail = sortedPaymentRecords.stream()
                            .flatMap(pr -> pr.getDetails().stream()) // 扁平化缴费记录的详情列表
                            .filter(pd -> feeType.getCode().equals(pd.getType()) && // 过滤出当前费用类型的详情
                                    StrUtil.isNotBlank(pd.getChangePaymentId()) && // 确保changePaymentId不为空
                                    associatedChangeRecordId.equals(pd.getChangePaymentId())) // 确保changePaymentId与关联的变更单ID匹配
                            .findFirst(); // 找按时间倒序的第一个匹配项

                    if (firstRelevantPaymentDetail.isPresent()) {
                        // 如果最新的缴费是关联到这个变更的，则累加所有关联到此变更的该费用类型的缴费
                        for (PaymentRecord pr : sortedPaymentRecords) { // 遍历所有缴费记录
                            for (PaymentRecord.Detail prDetail : pr.getDetails()) {
                                if (feeType.getCode().equals(prDetail.getType()) &&
                                        associatedChangeRecordId.equals(prDetail.getChangePaymentId()) &&
                                        prDetail.getPaymentAmount() != null) {
                                    paidAmount = paidAmount.add(prDetail.getPaymentAmount());
                                }
                            }
                        }
                    } else {
                        // 最新的缴费记录没有与此最新变更关联（或者根本没有缴费记录匹配），
                        // 则认为此变更后尚未缴费，paidAmount为0。
                        paidAmount = BigDecimal.ZERO;
                    }
                } else {
                    // 没有找到与当前feeType相关的有效变更单ID。
                    // 这种情况下，"基于变更的已缴费"为0。
                    paidAmount = BigDecimal.ZERO;
                }
            }
            detailToUpdate.setPaidAmount(paidAmount); // 设置到变更详情对象
        } else {
            // 没有历史变更单的情况下(即该费用类型没有变更过)，直接设置startDate=合同开始日期，paidAmount=所有缴费单的费用累加
            if (CollUtil.isNotEmpty(historicalPaymentRecords)) {
                // 累加该费用类型的所有已缴纳金额
                BigDecimal totalPaidAmount = BigDecimal.ZERO;
                for (PaymentRecord pr : historicalPaymentRecords) {
                    for (PaymentRecord.Detail prDetail : pr.getDetails()) {
                        if (feeType.getCode().equals(prDetail.getType()) && prDetail.getPaymentAmount() != null) {
                            totalPaidAmount = totalPaidAmount.add(prDetail.getPaymentAmount());
                        }
                    }
                }
                detailToUpdate.setPaidAmount(totalPaidAmount);

                // 设置startDate为合同开始日期
                detailToUpdate.setStartDate(dto.getContractStartDate());
            } else {
                // 如果没有历史缴费记录，设置为0和合同开始日期
                detailToUpdate.setPaidAmount(BigDecimal.ZERO);
                detailToUpdate.setStartDate(dto.getContractStartDate());
            }
        }
    }

    /**
     * 暂存费用变更单
     *
     * @param paymentChangeRecord 费用变更记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String draftPaymentChangeRecord(PaymentChangeRecord paymentChangeRecord) {
        // 设置为暂存状态
        paymentChangeRecord.setPaymentStatus("0");

        // 暂存时不执行账户变动和套餐变更，只保存基本信息
        paymentChangeRecord.setCreateTime(DateUtils.getNowDate());

        // 生成单号
        try {
            paymentChangeRecord.setCreateTime(DateUtils.getNowDate());
            paymentChangeRecord.setId(OrderUtils.getChangeBillCode());
            paymentChangeRecordMapper.insertPaymentChangeRecord(paymentChangeRecord);
        } catch (DuplicateKeyException e) {
            // 同步缓存中最新的单号
            String maxId = paymentChangeRecordMapper.selectMaxId();
            if (maxId != null) {
                OrderUtils.setNumber(OrderUtils.BILL_ORDER2, Integer.parseInt(maxId.split("-")[2]));
            }
            paymentChangeRecord.setId(OrderUtils.getBillCode());
            paymentChangeRecordMapper.insertPaymentChangeRecord(paymentChangeRecord);
        }

        return paymentChangeRecord.getId();
    }

    /**
     * 确认费用变更单
     *
     * @param id 费用变更记录ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int confirmPaymentChangeRecord(String id) {
        // 查询暂存记录
        PaymentChangeRecord draftRecord = paymentChangeRecordMapper.selectPaymentChangeRecordById(id);
        if (draftRecord == null) {
            throw new ServiceException("费用变更记录不存在");
        }

        if (!"0".equals(draftRecord.getPaymentStatus())) {
            throw new ServiceException("只能确认暂存状态的费用变更单");
        }

        // 设置为已确认状态
        draftRecord.setPaymentStatus("1");

        // 执行账户变动逻辑
        balanceChange(draftRecord);

        // 执行套餐变更逻辑
        dealWithFeeType(draftRecord);

        // 更新记录
        return paymentChangeRecordMapper.updatePaymentChangeRecord(draftRecord);
    }

    /**
     * 修改暂存费用变更单
     *
     * @param paymentChangeRecord 费用变更记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateDraftPaymentChangeRecord(PaymentChangeRecord paymentChangeRecord) {
        // 查询现有记录
        PaymentChangeRecord existingRecord = paymentChangeRecordMapper.selectPaymentChangeRecordById(paymentChangeRecord.getId());
        if (existingRecord == null) {
            throw new ServiceException("费用变更记录不存在");
        }

        if (!"0".equals(existingRecord.getPaymentStatus())) {
            throw new ServiceException("只能修改暂存状态的费用变更单");
        }

        // 保持暂存状态
        paymentChangeRecord.setPaymentStatus("0");

        // 更新记录
        paymentChangeRecordMapper.updatePaymentChangeRecord(paymentChangeRecord);

        return paymentChangeRecord.getId();
    }

    /**
     * 删除暂存费用变更单
     *
     * @param id 费用变更记录ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDraftPaymentChangeRecord(String id) {
        // 查询记录
        PaymentChangeRecord record = paymentChangeRecordMapper.selectPaymentChangeRecordById(id);
        if (record == null) {
            throw new ServiceException("费用变更记录不存在");
        }

        if (!"0".equals(record.getPaymentStatus())) {
            throw new ServiceException("只能删除暂存状态的费用变更单");
        }

        return paymentChangeRecordMapper.deletePaymentChangeRecordById(id);
    }

}

