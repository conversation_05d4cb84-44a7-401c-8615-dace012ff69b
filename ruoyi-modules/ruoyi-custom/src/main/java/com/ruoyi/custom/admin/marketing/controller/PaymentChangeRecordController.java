package com.ruoyi.custom.admin.marketing.controller;

import cn.hutool.core.map.MapUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.custom.admin.marketing.domain.PaymentChangeRecord;
import com.ruoyi.custom.admin.marketing.domain.PaymentChangeRecordSummary;
import com.ruoyi.custom.admin.marketing.resp.PaymentChangeManagementResp;
import com.ruoyi.custom.admin.marketing.service.IPaymentChangeRecordService;
import com.ruoyi.custom.admin.marketing.service.IPaymentChangeRecordSummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 费用变更单Controller
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@RestController
@RequestMapping("/paymentChangeRecord")
@Api(value = "费用变更记录", tags = "费用变更记录")
public class PaymentChangeRecordController extends BaseController {
    @Autowired
    private IPaymentChangeRecordService paymentChangeRecordService;

    @Autowired
    private IPaymentChangeRecordSummaryService paymentChangeRecordSummaryService;

    /**
     * 查询费用变更单列表
     */
    // @RequiresPermissions("custom:record:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询费用变更列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contractNumber", value = "合同编号", paramType = "query"),
            @ApiImplicitParam(name = "elderlyName", value = "老人姓名", paramType = "query"),
            @ApiImplicitParam(name = "paymentStatus", value = "缴费状态，字典：custom_payment_record_status；0：暂存，1：已确认", paramType = "query"),
            @ApiImplicitParam(name = "params.beginCreateTime", value = "创建开始时间", paramType = "query"),
            @ApiImplicitParam(name = "params.endCreateTime", value = "创建结束时间", paramType = "query")
    })
    public TableDataInfo list(PaymentChangeRecord paymentChangeRecord) {
        startPage();
        paymentChangeRecord.setParams(MapUtil.of("queryType", "1")); // 不包含餐费的账单
        List<PaymentChangeRecord> list = paymentChangeRecordService.selectPaymentChangeRecordList(paymentChangeRecord);
        return getDataTable(list);
    }

    /**
     * 查询餐费费用变更单列表
     */
    @GetMapping("/meal/list")
    @ApiOperation(value = "查询餐费费用变更单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contractNumber", value = "合同编号", paramType = "query"),
            @ApiImplicitParam(name = "elderlyName", value = "老人姓名", paramType = "query"),
            @ApiImplicitParam(name = "paymentStatus", value = "缴费状态，字典：custom_payment_record_status；0：暂存，1：已确认", paramType = "query"),
            @ApiImplicitParam(name = "params.beginCreateTime", value = "创建开始时间", paramType = "query"),
            @ApiImplicitParam(name = "params.endCreateTime", value = "创建结束时间", paramType = "query")
    })
    public TableDataInfo mealList(PaymentChangeRecord paymentChangeRecord) {
        startPage();
        paymentChangeRecord.setParams(MapUtil.builder(new HashMap<String, Object>()).put("queryType", "2").build()); // 只有餐费的账单
        List<PaymentChangeRecord> list = paymentChangeRecordService.selectPaymentChangeRecordList(paymentChangeRecord);
        return getDataTable(list);
    }

    /**
     * 获取费用变更单详细信息
     */
    // @RequiresPermissions("custom:record:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取费用变更单详细信息")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(paymentChangeRecordService.selectPaymentChangeRecordById(id));
    }

    /**
     * 根据合同id，生成账单变更信息
     */
    @GetMapping("/change/info")
    @ApiOperation(value = "根据合同id，生成账单变更信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contractNumber", value = "合同编号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "1：护理套餐、床位变更；2：餐费变更", required = true, dataType = "String")
    })
    public AjaxResult generatePaymentChangeInfo(String contractNumber, String type) {
        PaymentChangeRecord paymentChangeRecord = paymentChangeRecordService.generatePaymentChangeInfo(contractNumber, type);
        return AjaxResult.success(paymentChangeRecord);
    }

    /**
     * 费用变更（床位套餐、护理套餐、餐费套餐通用）- 现在为暂存逻辑
     */
    @PostMapping("/change")
    @ApiOperation(value = "费用变更（暂存）")
    public AjaxResult change(@RequestBody PaymentChangeRecord paymentChangeRecord) {
        return AjaxResult.success("操作成功", paymentChangeRecordService.draftPaymentChangeRecord(paymentChangeRecord));
    }

    /**
     * 暂存费用变更单
     */
    @PostMapping("/draft")
    @ApiOperation(value = "暂存费用变更单")
    public AjaxResult draftPaymentChange(@RequestBody PaymentChangeRecord paymentChangeRecord) {
        return AjaxResult.success("操作成功", paymentChangeRecordService.draftPaymentChangeRecord(paymentChangeRecord));
    }

    /**
     * 确认费用变更单
     */
    @PutMapping("/confirm/{id}")
    @ApiOperation(value = "确认费用变更单")
    public AjaxResult confirmPaymentChange(@PathVariable("id") String id) {
        return toAjax(paymentChangeRecordService.confirmPaymentChangeRecord(id));
    }

    /**
     * 修改暂存费用变更单
     */
    @PutMapping("/draft/{id}")
    @ApiOperation(value = "修改暂存费用变更单")
    public AjaxResult updateDraftPaymentChange(@PathVariable("id") String id, @RequestBody PaymentChangeRecord paymentChangeRecord) {
        paymentChangeRecord.setId(id);
        return AjaxResult.success("操作成功", paymentChangeRecordService.updateDraftPaymentChangeRecord(paymentChangeRecord));
    }

    /**
     * 删除暂存费用变更单
     */
    @DeleteMapping("/draft/{id}")
    @ApiOperation(value = "删除暂存费用变更单")
    public AjaxResult deleteDraftPaymentChange(@PathVariable("id") String id) {
        return toAjax(paymentChangeRecordService.deleteDraftPaymentChangeRecord(id));
    }

    /**
     * 费用变更汇总管理列表
     */
    @GetMapping("/managementList")
    @ApiOperation(value = "费用变更汇总管理列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contractNumber", value = "合同编号", paramType = "query"),
            @ApiImplicitParam(name = "customerName", value = "长者姓名", paramType = "query"),
            @ApiImplicitParam(name = "liveStatus", value = "客户状态", paramType = "query"),
            @ApiImplicitParam(name = "careLevel", value = "护理级别", paramType = "query"),
            @ApiImplicitParam(name = "roomType", value = "房间类型", paramType = "query"),
            @ApiImplicitParam(name = "bedNumber", value = "房间", paramType = "query"),
            @ApiImplicitParam(name = "params.paymentStatus", value = "状态，字典：custom_payment_record_status；0：暂存（未结算），1：已确认（已结算）", paramType = "query"),
            @ApiImplicitParam(name = "params.beginCreateTime", value = "创建开始时间", paramType = "query"),
            @ApiImplicitParam(name = "params.endCreateTime", value = "创建结束时间", paramType = "query")
    })
    public TableDataInfo managementList(PaymentChangeRecordSummary paymentChangeRecordSummary) {
        startPage();
        List<PaymentChangeManagementResp> list = paymentChangeRecordSummaryService.selectPaymentChangeManagementList(paymentChangeRecordSummary);
        return getDataTable(list);
    }

}

